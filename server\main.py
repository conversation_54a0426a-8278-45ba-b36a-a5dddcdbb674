from flask import Flask, request, jsonify, make_response, send_file
import os
import base64
import csv
import json
import time
from io import String<PERSON>
from werkzeug.utils import secure_filename
from flask_cors import CORS
from pdf2image import convert_from_path
from PIL import Image
import openai
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from openpyxl import load_workbook
from datetime import datetime
from urllib.parse import quote
import fitz  # PyMuPDF
from prompts import get_proposal_summary_prompt, get_financial_summary_prompt, get_proposal_extraction_prompts, get_financial_extraction_prompts
from dotenv import load_dotenv  # Add this import
from docx import Document
from docx.shared import Pt
import threading


app = Flask(__name__)
# Simplified CORS configuration - allow all API routes from frontend
CORS(app,
     origins=["http://localhost:5173"],
     methods=["GET", "POST", "OPTIONS"],
     allow_headers=["Content-Type"],
     supports_credentials=True)

UPLOAD_FOLDER_PROPOSALS = "static/uploads/proposals"
UPLOAD_FOLDER_FINANCIALS = "static/uploads/financials"
os.makedirs(UPLOAD_FOLDER_PROPOSALS, exist_ok=True)
os.makedirs(UPLOAD_FOLDER_FINANCIALS, exist_ok=True)

ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}
load_dotenv()  # Load environment variables from .env file
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    print("❌ WARNING: OPENAI_API_KEY not found in environment variables!")
else:
    print(f"✅ OpenAI API key loaded (length: {len(api_key)} characters)")
client = openai.OpenAI(api_key=api_key)  # Use environment variable for API key

# Temporary in-memory storage for extracted text
proposal_extract = {}
financials_extract = {}

# Global variable to store processing metrics for CSV logging
processing_metrics = {}
metrics_lock = threading.Lock()

# CSV logging configuration
CSV_LOG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static", "processing_log.csv")
CSV_HEADERS = [
    "timestamp",
    "filename",
    "file_type",
    "pdf_type",
    "doc_type",
    "num_pages",
    "pymupdf_time",
    "image_conversion_time",
    "image_encoding_time",
    "openai_extraction_time",
    "summarization_time",
    "total_processing_time"
]

def initialize_csv_log():
    """Initialize the CSV log file with headers if it doesn't exist."""
    if not os.path.exists(CSV_LOG_PATH):
        try:
            with open(CSV_LOG_PATH, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(CSV_HEADERS)
            print(f"📊 Initialized CSV log file: {CSV_LOG_PATH}")
        except Exception as e:
            print(f"❌ Failed to initialize CSV log: {str(e)}")

def log_to_csv(metrics_data):
    """Log processing metrics to CSV file."""
    try:
        with open(CSV_LOG_PATH, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            row = [
                metrics_data.get('timestamp', ''),
                metrics_data.get('filename', ''),
                metrics_data.get('file_type', ''),
                metrics_data.get('pdf_type', ''),
                metrics_data.get('doc_type', ''),
                metrics_data.get('num_pages', 0),
                metrics_data.get('pymupdf_time', 0),
                metrics_data.get('image_conversion_time', 0),
                metrics_data.get('image_encoding_time', 0),
                metrics_data.get('openai_extraction_time', 0),
                metrics_data.get('summarization_time', 0),
                metrics_data.get('total_processing_time', 0)
            ]
            writer.writerow(row)
        print(f"📊 Logged metrics to CSV: {metrics_data['filename']}")
    except Exception as e:
        print(f"❌ Failed to log to CSV: {str(e)}")



def timer_decorator(func_name):
    """Decorator to time function execution and print results."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            print(f"⏱️ Starting {func_name}...")
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                print(f"✅ {func_name} completed in {execution_time:.2f} seconds")

                # Store timing data for CSV logging
                with metrics_lock:
                    if 'current_file' in processing_metrics:
                        current_file = processing_metrics['current_file']
                        if current_file not in processing_metrics:
                            processing_metrics[current_file] = {}

                        # Map function names to CSV columns
                        current_metrics = processing_metrics[current_file]
                        doc_type = current_metrics.get('document_type', '')

                        if "PyMuPDF" in func_name:
                            processing_metrics[current_file]['pymupdf_time'] = execution_time
                        elif "PDF to Images" in func_name:
                            processing_metrics[current_file]['image_conversion_time'] = execution_time
                        elif "Image Encoding" in func_name:
                            processing_metrics[current_file]['image_encoding_time'] = execution_time
                        elif "Text Extraction from Images" in func_name:
                            # Map text extraction based on document type
                            if (doc_type == "proposal" and "Proposal Text Extraction from Images" == func_name) or \
                               (doc_type == "financial" and "Financial Text Extraction from Images" == func_name):
                                processing_metrics[current_file]['openai_extraction_time'] = execution_time
                                print(f"✅ {func_name} completed in {execution_time} seconds")
                        elif "Summarization" in func_name:
                            # Map summarization based on document type
                            if (doc_type == "proposal" and "Proposal Text Summarization" == func_name) or \
                               (doc_type == "financial" and "Financial Text Summarization" == func_name):
                                processing_metrics[current_file]['summarization_time'] = execution_time
                                print(f"✅ {func_name} completed in {execution_time} seconds")
                    else:
                        # Skip logging standalone summarization calls to avoid duplicate CSV entries
                        # Summarization timing will be included in the main file processing record
                        pass

                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                print(f"❌ {func_name} failed after {execution_time:.2f} seconds: {str(e)}")
                raise
        return wrapper
    return decorator

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

POPPLER_PATH = r"C:\poppler-24.08.0\Library\bin"  # Update this path if needed

@timer_decorator("PDF to Images Conversion")
def pdf_to_images(pdf_path, thread_count=4):
    """Converts PDF to images with better error handling for poppler and multithreading support."""
    try:
        print(f"🔄 Converting PDF to images using {thread_count} threads...")
        images = convert_from_path(
            pdf_path,
            dpi=75,
            poppler_path=POPPLER_PATH,
            thread_count=thread_count
        )

        # Store number of pages in metrics
        num_pages = len(images)
        print(f"📄 PDF has {num_pages} pages")

        with metrics_lock:
            if 'current_file' in processing_metrics:
                current_file = processing_metrics['current_file']
                if current_file in processing_metrics:
                    processing_metrics[current_file]['num_pages'] = num_pages

        return images
    except Exception as e:
        if "poppler" in str(e).lower() or "pdfinfo" in str(e).lower():
            print(f"❌ Poppler error: {str(e)}")
            print("💡 Solution: Install poppler-utils")
            print("   - Windows: Download from https://github.com/oschwartz10612/poppler-windows/releases/")
            print("   - Add poppler/bin to your system PATH")
            print("   - Or use: conda install -c conda-forge poppler")
            raise Exception(f"Poppler not found or not in PATH. {str(e)}")
        else:
            raise

def encode_image_base64(image, quality=85):
    """Encodes PIL image to base64 at original size."""
    from io import BytesIO

    print(f"� Encoding image at original size: {image.size}")

    buffered = BytesIO()
    # Use JPEG for better compression, PNG for transparency
    if image.mode in ('RGBA', 'LA'):
        image.save(buffered, format="PNG", optimize=True)
    else:
        # Convert to RGB if needed and save as JPEG for better compression
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image.save(buffered, format="JPEG", quality=quality, optimize=True)

    return base64.b64encode(buffered.getvalue()).decode("utf-8")

@timer_decorator("Image Encoding")
def encode_images_batch(images, quality=85):
    """Encodes multiple PIL images to base64 format and captures total encoding time."""
    print(f"🔄 Batch encoding {len(images)} images to base64...")
    base64_images = []
    for i, img in enumerate(images):
        base64_img = encode_image_base64(img, quality)
        base64_images.append(base64_img)
        print(f"Encoded image {i+1}/{len(images)} (size: {len(base64_img)} chars)")

    print(f"📏 Total base64 image sizes: {[len(b64) for b64 in base64_images]} characters")
    return base64_images

@timer_decorator("Proposal Text Extraction from Images")
def extract_text_from_images_proposal(images):
    """Extracts text from multiple proposal images using a single OpenAI Vision API call."""
    try:
        print(f"🖼️ Processing {len(images)} images for proposal extraction...")
        base64_images = encode_images_batch(images)

        system_prompt, user_prompt = get_proposal_extraction_prompts()
        print(f"System prompt length: {len(system_prompt)} characters")
        print(f"User prompt length: {len(user_prompt)} characters")

        content = [{"type": "text", "text": user_prompt}]
        for i, b64 in enumerate(base64_images):
            content.append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{b64}"}})
            print(f"🖼️ Added image {i+1} to content (size: {len(b64)} chars)")

        print(f"🔍 Sending {len(images)} images to OpenAI Vision API for proposal extraction...")
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content}
            ],
            max_completion_tokens=1000,
        )

        extracted_text = response.choices[0].message.content
        print(f"OpenAI Vision API response length: {len(extracted_text) if extracted_text else 0} characters")

        if not extracted_text or extracted_text.strip() == "":
            raise Exception("OpenAI Vision API returned empty response")

        return extracted_text
    except openai.RateLimitError as e:
        print(f"❌ OpenAI Rate Limit Error: {str(e)}")
        raise Exception(f"Rate limit exceeded: {str(e)}")
    except openai.APIError as e:
        print(f"❌ OpenAI API Error: {str(e)}")
        raise Exception(f"OpenAI API error: {str(e)}")
    except Exception as e:
        print(f"❌ Error in extract_text_from_images_proposal: {str(e)}")
        raise

@timer_decorator("Financial Text Extraction from Images")
def extract_text_from_images_financial(images):
    """Extracts text from multiple financial images using a single OpenAI Vision API call."""
    base64_images = encode_images_batch(images)
    system_prompt, user_prompt = get_financial_extraction_prompts()
    content = [{"type": "text", "text": user_prompt}]
    for b64 in base64_images:
        content.append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{b64}"}})
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": content}
        ],
        max_completion_tokens=1000,
    )
    return response.choices[0].message.content

@timer_decorator("PyMuPDF Text Extraction")
def extract_text_with_pymupdf(file_path):
    """Extracts text from text-based PDFs using PyMuPDF."""
    try:
        doc = fitz.open(file_path)
        num_pages = len(doc)
        print(f"PDF has {num_pages} pages")

        # Store number of pages in metrics
        with metrics_lock:
            if 'current_file' in processing_metrics:
                current_file = processing_metrics['current_file']
                if current_file in processing_metrics:
                    processing_metrics[current_file]['num_pages'] = num_pages

        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        return text
    except Exception as e:
        print(f"Error extracting text with PyMuPDF: {str(e)}")
        return None

@timer_decorator("DOCX Text Extraction")
def extract_text_from_docx(file_path):
    """Extracts text from a .docx Word document."""
    try:
        doc = Document(file_path)
        full_text = []
        for para in doc.paragraphs:
            full_text.append(para.text)
        return '\n'.join(full_text)
    except Exception as e:
        print(f"Error extracting text from docx: {str(e)}")
        return None

# Helper to batch images
def batch_list(lst, batch_size):
    for i in range(0, len(lst), batch_size):
        yield lst[i:i + batch_size]

def process_uploaded_file(file_path, filename, storage_dict, document_type="proposal", pdf_type=None):
    """Processes a PDF or Word file and stores extracted text temporarily in memory."""
    # Initialize CSV logging for this file
    start_time = time.time()

    # Initialize metrics for this file
    with metrics_lock:
        processing_metrics['current_file'] = filename
        processing_metrics[filename] = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'filename': filename,
            'file_type': '',
            'pdf_type': '',
            'doc_type': document_type,  # Document type for CSV column
            'num_pages': 0,
            'document_type': document_type,  # Store document type for summarization tracking
            'pymupdf_time': 0,
            'image_conversion_time': 0,
            'image_encoding_time': 0,
            'openai_extraction_time': 0,
            'summarization_time': 0,
            'total_processing_time': 0
        }

    try:
        ext = filename.rsplit('.', 1)[1].lower()

        # Set file type in metrics
        with metrics_lock:
            processing_metrics[filename]['file_type'] = ext

        if ext in ['pdf']:
            # Use the provided pdf_type, or detect if not provided (for backward compatibility)
            if pdf_type is None:
                pdf_type = detect_pdf_type(file_path)

            # Set PDF type in metrics
            with metrics_lock:
                processing_metrics[filename]['pdf_type'] = pdf_type

            if pdf_type == "text-based":
                print(f"Processing {filename} as text-based PDF using PyMuPDF")
                extracted_text = extract_text_with_pymupdf(file_path)
                if not extracted_text:
                    raise Exception("Failed to extract text with PyMuPDF")
            else:
                print(f"Processing {filename} as scanned PDF using OpenAI Vision API")
                images = pdf_to_images(file_path, thread_count=4)
                if document_type == "proposal":
                    extracted_text = extract_text_from_images_proposal(images)
                    print(f"Extracted text type: {type(extracted_text)}")
                    print(f"Extracted text length: {len(extracted_text) if extracted_text else 0}")
                    print(f"Extracted text is None: {extracted_text is None}")
                    print(f"Extracted text is empty string: {extracted_text == '' if extracted_text else 'N/A'}")
                    if not extracted_text or extracted_text.strip() == "":
                        raise Exception("No text extracted from proposal images")
                else:  # financial
                    # Batch images once tpm limit increases, currently at 200,000 tokens
                    batch_results = []
                    for batch in batch_list(images, 2):
                        batch_results.append(extract_text_from_images_financial(batch))
                    extracted_text = "\n".join(batch_results)
                    if not extracted_text or extracted_text.strip() == "":
                        raise Exception("No text extracted from financial images")
        elif ext in ['docx']:
            print(f"Processing {filename} as Word document (.docx)")
            with metrics_lock:
                processing_metrics[filename]['pdf_type'] = 'N/A'
                processing_metrics[filename]['num_pages'] = 1  # DOCX treated as 1 page for logging
            extracted_text = extract_text_from_docx(file_path)
            if not extracted_text:
                raise Exception("Failed to extract text from Word document")
        else:
            raise Exception("Unsupported file type")

        storage_dict[filename] = extracted_text

        # Calculate total processing time as sum of individual components
        with metrics_lock:
            metrics = processing_metrics[filename]
            total_time = (
                metrics.get('pymupdf_time', 0) +
                metrics.get('image_conversion_time', 0) +
                metrics.get('image_encoding_time', 0) +
                metrics.get('openai_extraction_time', 0) +
                metrics.get('summarization_time', 0)
            )
            processing_metrics[filename]['total_processing_time'] = total_time
            log_to_csv(processing_metrics[filename])
            # Clean up metrics for this file
            del processing_metrics[filename]
            if 'current_file' in processing_metrics:
                del processing_metrics['current_file']

        return extracted_text
    except Exception as e:
        # Log error case as well - calculate total as sum of components
        with metrics_lock:
            if filename in processing_metrics:
                metrics = processing_metrics[filename]
                total_time = (
                    metrics.get('pymupdf_time', 0) +
                    metrics.get('image_conversion_time', 0) +
                    metrics.get('image_encoding_time', 0) +
                    metrics.get('openai_extraction_time', 0) +
                    metrics.get('summarization_time', 0)
                )
                processing_metrics[filename]['total_processing_time'] = total_time
                log_to_csv(processing_metrics[filename])
                del processing_metrics[filename]
            if 'current_file' in processing_metrics:
                del processing_metrics['current_file']

        print(f"Error processing file {filename}: {str(e)}")
        return f"Error processing file: {str(e)}"

import re

def sanitize_json_response(response_text):
    """Extracts JSON content from an OpenAI response string"""
    json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
    return json_match.group(0) if json_match else None

def handle_api_error(e):
    """Handle OpenAI API errors and return appropriate response"""
    if hasattr(e, 'response'):
        error_data = e.response.json()
        if error_data.get('error', {}).get('code') == 'rate_limit_exceeded':
            return {
                "error": "Rate limit exceeded. Try again in 2 minutes",
                "error_type": "rate_limit",
                "status_code": 429,
                "details": "The API rate limit has been reached. Please wait 2 minutes before trying again."
            }
    return {
        "error": str(e),
        "error_type": "general",
        "status_code": 500,
        "details": "An unexpected error occurred"
    }

#def summarize_financial_text(text, model="o3-mini-2025-01-31"):
@timer_decorator("Financial Text Summarization")
def summarize_financial_text(text, model="gpt-4o-mini", max_tokens=500):

    """Summarizes extracted financial text and returns insights in structured JSON format."""
    try:
        messages = [{
            "role": "user",
            "content": get_financial_summary_prompt(text)
        }]

        response = client.chat.completions.create(
            model=model,
            messages=messages,
            #max_tokens=max_tokens,
            temperature=0.0
        )

        response_text = response.choices[0].message.content.strip()

        # Try to extract JSON if response contains other text
        json_match = re.search(r"(\{[\s\S]*\})", response_text)
        if json_match:
            response_text = json_match.group(0)

        # Validate JSON structure
        parsed_json = json.loads(response_text)
        if "financial_summary" not in parsed_json:
            raise ValueError("Missing 'financial_summary' key in response")

        return json.dumps(parsed_json)

    except openai.RateLimitError as e:
        error_response = handle_api_error(e)
        return json.dumps(error_response)
    except Exception as e:
        print(f"Error in summarize_financial_text: {str(e)}")
        return json.dumps({
            "financial_summary": [],
            "error": str(e),
            "error_type": "general",
            "status_code": 500
        })

@timer_decorator("Proposal Text Summarization")
def summarize_proposal(text, model="o4-mini"):
#def summarize_proposal(text, model="gpt-4.1-nano", max_tokens=500):

    try:
        messages = [{
            "role": "user",
            "content": get_proposal_summary_prompt(text)
        }]

        response = client.chat.completions.create(
            model=model,
            messages=messages,
            #max_tokens=max_tokens,
            #temperature=0.0,
        )

        response_text = response.choices[0].message.content.strip()

        # Try to extract JSON if response contains other text
        json_match = re.search(r"(\{[\s\S]*\})", response_text)
        if json_match:
            response_text = json_match.group(0)

        # Parse the response
        try:
            parsed_data = json.loads(response_text)
            
            # Handle array response
            if isinstance(parsed_data, list) and len(parsed_data) > 0:
                # Get the first item as our data source
                data_item = parsed_data[0]
                
                # Transform into required format
                formatted_response = {
                    "proposal_summary": {
                        "headers": [h for h in list(data_item.keys())[:-12] if h not in [
                            "Defamation Coverage",
                            "Loss of Documents",
                            "Errors and ommissions",
                            "Incoming/Outgoing partners",
                            "Dishonesty of employees",
                            "Breach of Authority"
                        ]],
                        "values": [v for h, v in zip(list(data_item.keys())[:-12], list(data_item.values())[:-12]) if h not in [
                            "Defamation Coverage",
                            "Loss of Documents",
                            "Errors and ommissions",
                            "Incoming/Outgoing partners",
                            "Dishonesty of employees",
                            "Breach of Authority"
                        ]]
                    },
                    "extensions": {
                        "headers": [
                            "Defamation Coverage",
                            "Loss of Documents",
                            "Errors and ommissions",
                            "Incoming/Outgoing partners",
                            "Dishonesty of employees",
                            "Breach of Authority"
                        ],
                        "values": [data_item.get(h, None) for h in [
                            "Defamation Coverage",
                            "Loss of Documents",
                            "Errors and ommissions",
                            "Incoming/Outgoing partners",
                            "Dishonesty of employees",
                            "Breach of Authority"
                        ]]
                    },
                    "insurance_history": {
                        "headers": list(data_item.keys())[-6:],  # Last 6
                        "values": list(data_item.values())[-6:]  # Last 6
                    }
                }
                
                return json.dumps(formatted_response)
                
            # Handle direct object response
            elif isinstance(parsed_data, dict):
                if "proposal_summary" in parsed_data and "insurance_history" in parsed_data:
                    # If the data is in the old format (all fields in proposal_summary)
                    if "extensions" not in parsed_data:
                        # Extract extension fields from proposal_summary
                        extension_fields = [
                            "Defamation Coverage",
                            "Loss of Documents",
                            "Errors and ommissions",
                            "Incoming/Outgoing partners",
                            "Dishonesty of employees",
                            "Breach of Authority"
                        ]
                        
                        # Create extensions section
                        extension_indices = [i for i, h in enumerate(parsed_data["proposal_summary"]["headers"]) if h in extension_fields]
                        extension_values = [parsed_data["proposal_summary"]["values"][i] for i in extension_indices]
                        
                        # Remove extension fields from proposal_summary
                        new_headers = [h for i, h in enumerate(parsed_data["proposal_summary"]["headers"]) if h not in extension_fields]
                        new_values = [v for i, v in enumerate(parsed_data["proposal_summary"]["values"]) if i not in extension_indices]
                        
                        parsed_data["proposal_summary"]["headers"] = new_headers
                        parsed_data["proposal_summary"]["values"] = new_values
                        
                        # Add extensions section
                        parsed_data["extensions"] = {
                            "headers": extension_fields,
                            "values": extension_values
                        }
                    
                    # Convert any hyphen values to null and remove commas from numeric values
                    for section in ["proposal_summary", "extensions", "insurance_history"]:
                        if "values" in parsed_data[section]:
                            parsed_data[section]["values"] = [
                                None if v == "-" else float(str(v).replace(',', '')) if isinstance(v, (int, float)) or (isinstance(v, str) and v.replace(',', '').replace('.', '').isdigit()) else v 
                                for v in parsed_data[section]["values"]
                            ]
                    return json.dumps(parsed_data)
                else:
                    # Transform single object into required format
                    keys = list(parsed_data.keys())
                    values = list(parsed_data.values())
                    
                    # Separate extensions from main proposal data
                    extension_keys = [
                        "Defamation Coverage",
                        "Loss of Documents",
                        "Errors and ommissions",
                        "Incoming/Outgoing partners",
                        "Dishonesty of employees",
                        "Breach of Authority"
                    ]
                    
                    main_keys = [k for k in keys[:-6] if k not in extension_keys]
                    main_values = [v for k, v in zip(keys[:-6], values[:-6]) if k not in extension_keys]
                    
                    extension_values = [parsed_data.get(k, None) for k in extension_keys]
                    
                    formatted_response = {
                        "proposal_summary": {
                            "headers": main_keys,
                            "values": [None if v == "-" else float(str(v).replace(',', '')) if isinstance(v, (int, float)) or (isinstance(v, str) and v.replace(',', '').replace('.', '').isdigit()) else v for v in main_values]
                        },
                        "extensions": {
                            "headers": extension_keys,
                            "values": [None if v == "-" else float(str(v).replace(',', '')) if isinstance(v, (int, float)) or (isinstance(v, str) and v.replace(',', '').replace('.', '').isdigit()) else v for v in extension_values]
                        },
                        "insurance_history": {
                            "headers": keys[-6:],
                            "values": [None if v == "-" else float(str(v).replace(',', '')) if isinstance(v, (int, float)) or (isinstance(v, str) and v.replace(',', '').replace('.', '').isdigit()) else v for v in values[-6:]]
                        }
                    }
                    return json.dumps(formatted_response)
                    
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {str(e)}")
            return json.dumps({
                "proposal_summary": {
                    "headers": [],
                    "values": []
                },
                "insurance_history": {
                    "headers": [],
                    "values": []
                },
                "error": "Failed to parse API response"
            })

    except openai.RateLimitError as e:
        error_response = handle_api_error(e)
        return json.dumps(error_response)
    except Exception as e:
        print(f"Error in summarize_proposal: {str(e)}")
        return json.dumps({
            "error": str(e),
            "error_type": "general",
            "status_code": 500
        })

def summarize_proposal_with_retry(text, max_retries=2):
    """
    Attempts to summarize the proposal text multiple times if the initial attempt fails.
    """
    for attempt in range(max_retries):
        try:
            result = summarize_proposal(text)
            if result and result != "null":
                return result
            print(f"Attempt {attempt + 1} failed. Retrying...")
        except Exception as e:
            print(f"Error in summarize_proposal_with_retry: {str(e)}")
            print(f"Attempt {attempt + 1} failed. Retrying...")
    return None

# Define the absolute path to rules.txt
BASE_DIR = os.path.dirname(os.path.abspath(__file__))  # Get the directory where main.py is located
RULES_FILE_PATH = os.path.join(BASE_DIR, "static", "ratingGuide", "rules.txt")



class QuotationCalculator:
    @staticmethod
    def get_annual_fees_rate(annual_fees: float) -> float:
        match annual_fees:
            case annual_fees if annual_fees < 1_000_001:
                return 0.0150
            case annual_fees if annual_fees < 2_000_001:
                return 0.01050
            case annual_fees if annual_fees < 5_000_001:
                return 0.00750
            case annual_fees if annual_fees < 10_000_001:
                return 0.00450
            case annual_fees if annual_fees < 20_000_001:
                return 0.00350
            case annual_fees if annual_fees < 50_000_001:
                return 0.00225
            case _:
                return 0.00115

    @staticmethod
    def get_limit_of_indemnity_rate(limit_of_indemnity: float) -> float:
        match limit_of_indemnity:
            case limit_of_indemnity if limit_of_indemnity < 1_000_001:
                return 1
            case limit_of_indemnity if limit_of_indemnity < 2_500_001:
                return 1.5
            case limit_of_indemnity if limit_of_indemnity < 5_000_001:
                return 1.9
            case limit_of_indemnity if limit_of_indemnity < 10_000_001:
                return 2.3
            case limit_of_indemnity if limit_of_indemnity < 20_000_001:
                return 2.75
            case limit_of_indemnity if limit_of_indemnity < 40_000_001:
                return 3.25
            case limit_of_indemnity if limit_of_indemnity < 60_000_001:
                return 3.65
            case _:
                return 4.5

    @staticmethod
    def calculate_deductible_discount(deductible: float, limit_of_indemnity: float) -> float:
        """
        Calculate the discount based on deductible/excess percentage of limit of indemnity.
        
        Args:
            deductible (float): The deductible/excess amount
            limit_of_indemnity (float): The limit of indemnity amount
            
        Returns:
            float: The discount percentage (0.0 to 1.0)
        """
        if not deductible or not limit_of_indemnity:
            return 0.0
            
        deductible_percentage = (deductible / limit_of_indemnity) * 100
        
        match deductible_percentage:
            case p if p <= 1:
                return 0.0  # No discount for 1% or less
            case p if p <= 2:
                return 0.20  # 20% discount for 2%
            case p if p <= 3:
                return 0.25  # 25% discount for 3%
            case p if p <= 4:
                return 0.33  # 33% discount for 4%
            case _:
                return 0.0  # No discount for more than 4%

def calculate_quotation(proposal_summary):
    try:
        # Get the first file's data (assuming single file for now)
        file_data = next(iter(proposal_summary.values()))
        
        # Get the proposal summary data
        proposal_data = file_data.get('proposal_summary', {})
        headers = proposal_data.get('headers', [])
        values = proposal_data.get('values', [])
        data_map = dict(zip(headers, values))

        # Also build a map for extensions section
        extensions_data = file_data.get('extensions', {})
        extensions_headers = extensions_data.get('headers', [])
        extensions_values = extensions_data.get('values', [])
        data_map_extensions = dict(zip(extensions_headers, extensions_values))

        # Helper function to safely convert string values to integers
        def safe_int_convert(value, default=0):
            if value in ['-', '', None]:
                return default
            try:
                return int(float(str(value).replace(',', '')))
            except ValueError:
                return default

        # Helper function to safely convert string values to floats
        def safe_float_convert(value, default=0.0):
            if value in ['-', '', None]:
                return default
            try:
                return float(str(value).replace(',', ''))
            except ValueError:
                return default

        # Debug logging for available keys
        print("\n=== Available Data Keys Debug ===")
        print(f"Available keys in data_map: {list(data_map.keys())}")

        # Extract required values from the mapped data
        partners_principals = safe_int_convert(data_map.get('Number of Business Partners', 0))
        qualified_assistants = safe_int_convert(data_map.get('Number of Qualified staff', 0))
        unqualified_assistants = safe_int_convert(data_map.get('Number of Unqualified staff', 0))
        #other_staff = safe_int_convert(data_map.get('Other staff', 0))

        # Debug logging for staff numbers
        print("\n=== Staff Numbers Debug ===")
        print(f"Partners/Principals: {partners_principals}")
        print(f"Qualified Staff: {qualified_assistants}")
        print(f"Unqualified Staff: {unqualified_assistants}")
        #print(f"Other Staff: {other_staff}")
        
        # Extract and clean estimated annual fee and limit of indemnity
        estimated_annual_fee = safe_float_convert(data_map.get('Estimated Annual Income', '0'))
        limit_of_indemnity = safe_float_convert(data_map.get('Limit of Indemnity (Cover Limit)', '0'))
        deductible = safe_float_convert(data_map.get('Deductible/Excess Applicable', '0'))
        
        # Calculate deductible discount
        deductible_discount = QuotationCalculator.calculate_deductible_discount(deductible, limit_of_indemnity)
        deductible_percentage = (deductible / limit_of_indemnity * 100) if deductible and limit_of_indemnity else 0
        
        # Debug logging for annual fee and limit
        print("\n=== Annual Fee and Limit Debug ===")
        print(f"Estimated Annual Fee: {estimated_annual_fee}")
        print(f"Limit of Indemnity: {limit_of_indemnity}")
        print(f"Deductible: {deductible}")
        print(f"Deductible Percentage: {deductible_percentage}%")
        print(f"Deductible Discount: {deductible_discount * 100}%")
        
        # Extract boolean values
        def get_bool_value(value):
            if value is None:
                return False
            return str(value).lower() == 'yes'

        loss_of_documents = get_bool_value(data_map_extensions.get('Loss of Documents', data_map.get('Loss of Documents')))
        libel_slander = get_bool_value(data_map_extensions.get('Defamation Coverage', data_map.get('Defamation Coverage')))
        dishonesty_employees = get_bool_value(data_map_extensions.get('Dishonesty of employees', data_map.get('Dishonesty of employees')))
        incoming_outgoing_partners = get_bool_value(data_map_extensions.get('Incoming/Outgoing partners', data_map.get('Incoming/Outgoing partners')))
        errors_and_ommissions = get_bool_value(data_map_extensions.get('Errors and ommissions', data_map.get('Errors and ommissions')))
        breach_of_authority = get_bool_value(data_map_extensions.get('Breach of Authority', data_map.get('Breach of Authority')))
        
        # Extract and clean profession
        profession = str(data_map.get('Occupation of the Insured', '')).lower()
        profession_temp = str(data_map.get('Occupation of the Insured', '')).lower()

        # Extract retroactive period
        retroactive_period = safe_int_convert(data_map.get('Retroactive period', 0))

        # Debug logging for retroactive period
        print(f"\n=== Retroactive Period Debug ===")
        print(f"Retroactive Period: {retroactive_period} months")

        # Get rates based on annual fees and limit of indemnity
        annual_fees_rate = QuotationCalculator.get_annual_fees_rate(estimated_annual_fee)
        annual_fees_rate_percentage = annual_fees_rate * 100  # Convert to percentage
        limit_rate = QuotationCalculator.get_limit_of_indemnity_rate(limit_of_indemnity)
        limit_rate_percentage = limit_rate * 100  # Convert to percentage

        # Debug logging for rates
        print("\n=== Rates Debug ===")
        print(f"Annual Fees Rate: {annual_fees_rate}")
        print(f"Annual Fees Rate Percentage: {annual_fees_rate_percentage}%")
        print(f"Limit Rate: {limit_rate}")
        print(f"Limit Rate Percentage: {limit_rate_percentage}%")

        # Compute intermediary values
        temp = estimated_annual_fee * annual_fees_rate
        
        # Calculate staff contributions
        partners_contribution = partners_principals * 3600
        qualified_contribution = qualified_assistants * 3000
        unqualified_contribution = unqualified_assistants * 2000
        #other_contribution = other_staff * 1000
        
        # Debug logging for staff contributions
        print("\n=== Staff Contributions Debug ===")
        print(f"Partners Contribution: {partners_contribution}")
        print(f"Qualified Staff Contribution: {qualified_contribution}")
        print(f"Unqualified Staff Contribution: {unqualified_contribution}")
       # print(f"Other Staff Contribution: {other_contribution}")
        
        part_a = (temp + partners_contribution + qualified_contribution + 
                 unqualified_contribution)# + other_contribution)
        
        # Debug logging for part_a calculation
        print("\n=== Part A Calculation Debug ===")
        print(f"Annual Fee Component (temp): {temp}")
        print(f"Total Staff Contribution: {partners_contribution + qualified_contribution + unqualified_contribution}")# + other_contribution}")
        print(f"Part A Total: {part_a}")

        part_b = part_a * limit_rate
        print(f"Part B (Part A * Limit Rate): {part_b}")

        # Determine the profession factor (N)
        profession_factor_map = {
            "opticians": 1, "chemists": 1, "accountants": 1, "auditors": 1, "attorneys": 1,
            "architects": 1.35, "civil engineers": 1.35, "quantity surveyors": 1.35,
            "dentists": 1.75, "doctors": 1.75, "surgeons": 1.75,
            "certified public accountants": 1, "cybersecurity": 1, "auctioneers": 1, "it": 1
        }
        # Use the original profession name for display but get the factor from the map
        N = profession_factor_map.get(profession, 1)  # Default to 1 if profession not in map
        profession_factor_percentage = N * 100  # Convert to percentage
        print(f"Profession Factor (N): {N}")
        print(f"Profession Factor Percentage: {profession_factor_percentage}%")
        print(f"Original Profession: {profession_temp}")  # Log the original profession name

        part_c = part_b * N
        print(f"Part C (Part B * N): {part_c}")

        part_d = part_a + part_b + part_c
        print(f"Part D (Part A + B + C): {part_d}")

        part_e = part_d * 0.1 if loss_of_documents else 0
        part_f = part_d * 0.1 if libel_slander else 0
        part_g = part_d * 0.1 if dishonesty_employees else 0
        part_h = part_d * 0.1 if incoming_outgoing_partners else 0
        part_i = part_d * 0.1 if errors_and_ommissions else 0
        part_j = part_d * 0.1 if breach_of_authority else 0


        # Debug logging for extensions
        print("\n=== Extensions Debug ===")
        print(f"Loss of Documents (Part E): {part_e}")
        print(f"Libel & Slander (Part F): {part_f}")
        print(f"Dishonesty of Employees (Part G): {part_g}")
        print(f"Incoming/Outgoing Partners (Part H): {part_h}")
        print(f"Retroactive Errors (Part I): {part_i}")

        part_k = part_d + part_e + part_f + part_g + part_h + part_i + part_j

        # Apply retroactive period logic
        retroactive_adjustment = 0  # Initialize the variable
        if retroactive_period == 12:
            # 12 Months: additional 40% of annual premium
            retroactive_adjustment = part_k * 0.40
            part_k = part_k + retroactive_adjustment
            print(f"\n=== Retroactive Period Applied ===")
            print(f"Retroactive Period: {retroactive_period} months")
            print(f"Additional Premium (40%): {retroactive_adjustment}")
            print(f"New Part K: {part_k}")
        elif retroactive_period == 24:
            # 24 Months: additional 65% of annual premium
            retroactive_adjustment = part_k * 0.65
            part_k = part_k + retroactive_adjustment
            print(f"\n=== Retroactive Period Applied ===")
            print(f"Retroactive Period: {retroactive_period} months")
            print(f"Additional Premium (65%): {retroactive_adjustment}")
            print(f"New Part K: {part_k}")
        else:
            print(f"\n=== No Retroactive Period Applied ===")
            print(f"Retroactive Period: {retroactive_period} months (no adjustment)")

        # Calculate deductible discount
        part_l = -part_k * deductible_discount if deductible_discount > 0 else 0
        part_m = part_k + part_l  # part_l is negative, so we add it
        
        print(f"\n=== Deductible Discount Applied ===")
        print(f"Original Premium (Part K): {part_k}")
        print(f"Discount Amount (Part L): {part_l}")
        print(f"Discounted Premium (Part M): {part_m}")
        
        levies = part_m * 0.0045
        stamp_duty = 40
        total_premium = part_m + levies + stamp_duty

        # Debug logging for final calculations
        print("\n=== Final Calculations Debug ===")
        print(f"Part K (Basic Premium): {part_k}")
        print(f"Part L (Discount): {part_l}")
        print(f"Part M (Discounted Premium): {part_m}")
        print(f"Levies: {levies}")
        print(f"Stamp Duty: {stamp_duty}")
        print(f"Total Premium: {total_premium}")

#      "Name of Proposer / Insured", #"Name of Cedant / Reinsured",
        return {
            "quotation_calculation": {
                "company_name": data_map.get('Name of Cedant / Reinsured', ''),
                "proposer_name": data_map.get('Name of Proposer / Insured', ''),
                "broker_name": data_map.get('Name of Broker', ''),
                "period_of_cover": data_map.get('Period of Cover ', ''),
                "estimated_annual_fee": estimated_annual_fee,
                "annual_fees_rate_percentage": round(annual_fees_rate_percentage, 3),
                "limit_of_indemnity": limit_of_indemnity,
                "limit_rate_percentage": round(limit_rate_percentage, 3),
                "profession": profession_temp,
                "profession_factor_percentage": round(profession_factor_percentage, 3),
                "deductible": deductible,
                "deductible_percentage": round(deductible_percentage, 2),
                "deductible_discount": round(deductible_discount * 100, 2),
                "retroactive_period": retroactive_period,
                "retroactive_adjustment": round(retroactive_adjustment, 2),
                "staff_breakdown": {
                    "partners_principals": partners_principals,
                    "qualified_assistants": qualified_assistants,
                    "unqualified_assistants": unqualified_assistants,
                    #"other_staff": other_staff
                }
            },
            "variables": {
                "temp": round(temp, 2),
                "part_a": round(part_a, 2),
                "part_b": round(part_b, 2),
                "part_c": round(part_c, 2),
                "part_d": round(part_d, 2),
                "part_e": round(part_e, 2),
                "part_f": round(part_f, 2),
                "part_g": round(part_g, 2),
                "part_h": round(part_h, 2),
                "part_i": round(part_i, 2),
                "part_j": round(part_j, 2),
                "part_k": round(part_k, 2),
                "part_l": round(part_l, 2),
                "part_m": round(part_m, 2),
                "levies": round(levies, 2),
                "stamp_duty": stamp_duty,
                "total_premium": round(total_premium, 2)
            }
        }

    except Exception as e:
        print(f"Error in calculate_quotation: {str(e)}")
        return {"error": f"Calculation error: {str(e)}"}

############################################################################################################################################################################################
from flask import Flask, jsonify

app = Flask(__name__)
proposal_summary = None
financials_summary = None

@app.route('/upload/proposal', methods=['POST'])
def upload_proposal():
    """Process proposal files with multithreading for improved performance."""
    try:
        if 'files' not in request.files:
            return jsonify({"error": "No files provided"}), 400

        files = request.files.getlist('files')
        if not files:
            return jsonify({"error": "No files received"}), 400

        print(f"🔄 Processing {len(files)} proposal files with multithreading...")

        # Use the concurrent processing function we already implemented
        result, status_code = process_proposal_files_concurrent(files)

        # If it's a dictionary with an error, return it
        if isinstance(result, dict) and "error" in result:
            return jsonify(result), status_code

        return jsonify(result), status_code

    except Exception as e:
        print(f"Server error in upload_proposal: {str(e)}")
        return jsonify({
            "error": "Server error",
            "error_type": "general",
            "status_code": 500
        }), 500

@app.route('/upload/financials', methods=['POST'])
def upload_financials():
    """Process financial files with multithreading for improved performance."""
    try:
        if 'files' not in request.files:
            return jsonify({"error": "No files provided"}), 400

        files = request.files.getlist('files')
        if not files:
            return jsonify({"error": "No files received"}), 400

        print(f"🔄 Processing {len(files)} financial files with multithreading...")

        # Use the concurrent processing function we already implemented
        result, status_code = process_financial_files_concurrent(files)

        # If it's a dictionary with an error, return it
        if isinstance(result, dict) and "error" in result:
            return jsonify(result), status_code

        return jsonify(result), status_code

    except Exception as e:
        print(f"Server error in upload_financials: {str(e)}")
        return jsonify({
            "error": "Server error",
            "details": str(e),
            "status": "failed"
        }), 500

def process_proposal_files_concurrent(files):
    """Process proposal files concurrently and return combined results."""
    global proposal_summary
    proposal_extract.clear()  # Clear proposal extraction memory before each upload

    def process_single_proposal_file(file):
        """Process a single proposal file."""
        try:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(UPLOAD_FOLDER_PROPOSALS, filename)
                file.save(file_path)

                # Detect PDF type and print information
                pdf_type = detect_pdf_type(file_path) if filename.lower().endswith('.pdf') else None
                print(f"\n=== File Analysis for {filename} ===")
                print(f"File Type: {pdf_type if pdf_type else 'docx'}")

                # Process file and extract text
                extracted_text = process_uploaded_file(file_path, filename, proposal_extract, document_type="proposal", pdf_type=pdf_type)
                if extracted_text and "Error processing file" not in extracted_text:
                    return f"[FILE: {filename}]\n{extracted_text}", filename
                else:
                    print(f"Failed to extract text from {filename}")
                    return None, filename
        except Exception as e:
            print(f"Error processing proposal file {getattr(file, 'filename', 'unknown')}: {str(e)}")
            return None, getattr(file, 'filename', 'unknown')

    # Process files concurrently using ThreadPoolExecutor
    extracted_texts = []
    filenames = []

    with ThreadPoolExecutor(max_workers=4) as executor:
        # Submit all file processing tasks
        future_to_file = {executor.submit(process_single_proposal_file, file): file for file in files}

        # Collect results as they complete
        for future in as_completed(future_to_file):
            extracted_text, filename = future.result()
            if extracted_text:
                extracted_texts.append(extracted_text)
                filenames.append(filename)

    if not extracted_texts:
        return {"error": "No valid proposal files processed.", "status": "failed"}, 400

    # Combine all extracted texts (with file markers)
    combined_text = "\n\n".join(extracted_texts)

    # Summarize the combined text
    structured_data = summarize_proposal_with_retry(combined_text)
    if not structured_data:
        return {
            "error": "No response from summarization API",
            "error_type": "general",
            "status_code": 500
        }, 500

    # Parse and store as a single summary
    try:
        json_data = json.loads(structured_data)
        proposal_summary = {"combined": json_data}
        return {
            "message": "Proposal files processed",
            "results": proposal_summary,
            "status": "complete"
        }, 200
    except json.JSONDecodeError as e:
        print(f"JSON parsing error for combined summary: {str(e)}")
        return {
            "error": "Failed to parse JSON response",
            "error_type": "general",
            "status_code": 500
        }, 500

def process_financial_files_concurrent(files):
    """Process financial files concurrently and return results."""
    financials_extract.clear()  # Clear financial extraction memory before each upload

    def process_single_financial_file(file):
        """Process a single financial file."""
        try:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(UPLOAD_FOLDER_FINANCIALS, filename)
                file.save(file_path)

                pdf_type = detect_pdf_type(file_path) if filename.lower().endswith('.pdf') else None
                print(f"\n=== File Analysis for {filename} ===")
                print(f"File Type: {pdf_type if pdf_type else 'docx'}")

                extracted_text = process_uploaded_file(file_path, filename, financials_extract, document_type="financial", pdf_type=pdf_type)
                if not extracted_text:
                    return {
                        "error": "Failed to extract text from file",
                        "file_type": pdf_type if pdf_type else 'docx'
                    }, filename

                structured_data = summarize_financial_text(extracted_text)
                json_data = json.loads(structured_data)

                # Add PDF type information to the results
                if isinstance(json_data, dict):
                    json_data["pdf_type"] = pdf_type
                    # Inject filename into each year of financial_summary
                    if "financial_summary" in json_data:
                        for year_obj in json_data["financial_summary"]:
                            year_obj["source_file"] = filename
                    # Check for rate limit error
                    if json_data.get("error_type") == "rate_limit":
                        return {
                            "error": json_data["error"],
                            "error_type": "rate_limit",
                            "status_code": 429
                        }, filename

                return json_data, filename
        except Exception as e:
            print(f"Error processing financial file {file.filename}: {str(e)}")
            return {
                "error": "Failed to process file",
                "details": str(e)
            }, file.filename

    # Process files concurrently using ThreadPoolExecutor
    results = {}

    with ThreadPoolExecutor(max_workers=4) as executor:
        # Submit all file processing tasks
        future_to_file = {executor.submit(process_single_financial_file, file): file for file in files}

        # Collect results as they complete
        for future in as_completed(future_to_file):
            result, filename = future.result()
            results[filename] = result

            # Check for rate limit error and return immediately if found
            if isinstance(result, dict) and result.get("error_type") == "rate_limit":
                return {
                    "error": result["error"],
                    "error_type": "rate_limit",
                    "status_code": 429
                }, 429

    # Merge all years from all files into a single list
    all_years = []
    for file_result in results.values():
        if isinstance(file_result, dict) and "financial_summary" in file_result:
            all_years.extend(file_result["financial_summary"])

    return {
        "message": "Financial files processed",
        "financials_summary": {"financial_summary": all_years},
        "status": "complete"
    }, 200

@app.route('/generate_quote', methods=['POST'])
def generate_quote():
    global proposal_summary
    try:
        if not proposal_summary:
            return jsonify({"error": "No proposal data available. Please upload a proposal first."}), 400
        
        print("Processing proposal data:", proposal_summary)
        
        # Use calculate_quotation function directly
        quotation_dict = calculate_quotation(proposal_summary)
        
        # Check for calculation errors
        if "error" in quotation_dict:
            return jsonify({"error": quotation_dict["error"]}), 400

        # Ensure all required variables are present
        required_variables = [
            "part_a", "part_b", "part_c", "part_d",
            "part_e", "part_f", "part_g", "part_h", "part_i", "part_j", "part_k", "part_l", "part_m", "levies",
            "stamp_duty", "total_premium"
        ]

        if "variables" not in quotation_dict:
            quotation_dict["variables"] = {}

        for var in required_variables:
            if var not in quotation_dict["variables"]:
                quotation_dict["variables"][var] = 0

        # Add formatted numbers for display
        display_values = {
            "estimated_annual_fee": format_number_with_commas(quotation_dict["quotation_calculation"]["estimated_annual_fee"]),
            "limit_of_indemnity": format_number_with_commas(quotation_dict["quotation_calculation"]["limit_of_indemnity"]),
            "part_a": format_number_with_commas(quotation_dict["variables"]["part_a"]),
            "part_b": format_number_with_commas(quotation_dict["variables"]["part_b"]),
            "part_c": format_number_with_commas(quotation_dict["variables"]["part_c"]),
            "part_d": format_number_with_commas(quotation_dict["variables"]["part_d"]),
            "part_e": format_number_with_commas(quotation_dict["variables"]["part_e"]),
            "part_f": format_number_with_commas(quotation_dict["variables"]["part_f"]),
            "part_g": format_number_with_commas(quotation_dict["variables"]["part_g"]),
            "part_h": format_number_with_commas(quotation_dict["variables"]["part_h"]),
            "part_i": format_number_with_commas(quotation_dict["variables"]["part_i"]),
            "part_j": format_number_with_commas(quotation_dict["variables"]["part_j"]),
            "part_k": format_number_with_commas(quotation_dict["variables"]["part_k"]),
            "part_l": format_number_with_commas(quotation_dict["variables"]["part_l"]),
            "part_m": format_number_with_commas(quotation_dict["variables"]["part_m"]),
            "levies": format_number_with_commas(quotation_dict["variables"]["levies"]),
            "stamp_duty": format_number_with_commas(quotation_dict["variables"]["stamp_duty"]),
            "total_premium": format_number_with_commas(quotation_dict["variables"]["total_premium"])
        }

        print("Formatted display values:", display_values)
        quotation_dict["display_values"] = display_values

        print("Final quotation dict:", quotation_dict)
        return jsonify(quotation_dict)

    except Exception as e:
        print(f"Server error in generate_quote: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Add a route to clear the proposal summary if needed
@app.route('/clear_proposal', methods=['POST'])
def clear_proposal():
    global proposal_summary
    proposal_summary = None
    return jsonify({"message": "Proposal data cleared"}), 200

@app.route('/test_openai', methods=['GET'])
def test_openai():
    """Test endpoint to verify OpenAI API connectivity."""
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "user", "content": "Say 'Hello, OpenAI API is working!'"}
            ],
            max_completion_tokens=50,
        )
        return jsonify({
            "status": "success",
            "message": "OpenAI API is working correctly",
            "response": response.choices[0].message.content
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"OpenAI API test failed: {str(e)}"
        }), 500

def populate_excel_template(quotation_data):
    try:
        # Note the space before .xlsx in the filename
        template_path = os.path.join(os.path.dirname(__file__), 'static', 'PI QUOTATION TEMPLATE .xlsx')
        
        if not os.path.exists(template_path):
            print(f"Template not found at: {template_path}")
            return None
            
        print(f"Loading template from: {template_path}")
        wb = load_workbook(template_path)
        ws = wb.active
        
        # Map the variables to their respective cells
        cell_mappings = {
            'company_name': 'B7',
            'broker_name': 'B9',
            'proposer_name': 'B5',
            'estimated_annual_fee': 'C17',
            'annual_fees_rate_percentage': 'D17',  # Add annual fees rate percentage
            'limit_of_indemnity': 'C20',
            'limit_rate_percentage': 'D20',
            'profession': 'C22',
            'profession_factor_percentage': 'D22',
            'partners_principals': 'E12',
            'qualified_assistants': 'E13',
            'unqualified_assistants': 'E14',
            'part_a': 'F18',
            'part_b': 'F20',
            'part_c': 'F22',
            'part_d': 'F24',
            'part_e': 'F26',
            'part_f': 'F27',
            'part_g': 'F28',
            'part_h': 'F29',
            'part_i': 'F30',
            'part_j': 'F31',
            'part_k': 'F32',
            'deductible': 'D33',
            'deductible_percentage': 'B50',
            'part_l': 'F35',
            'part_m': 'F36',
            'levies': 'F37',
            'stamp_duty': 'F38',
            'total_premium': 'F39'
        }
        
        # Populate cells
        for key, cell in cell_mappings.items():
            if key in quotation_data.get('quotation_calculation', {}):
                value = quotation_data['quotation_calculation'][key]
                if key in ['profession_factor_percentage', 'limit_rate_percentage', 'annual_fees_rate_percentage', 'deductible_percentage']:
                    # Write as decimal (e.g., 0.00225) and use 0.00% format
                    ws[cell] = quotation_data['quotation_calculation'][key] / 100  # Convert percentage to decimal
                    ws[cell].number_format = '0.000%'
                elif key == 'deductible':
                    ws[cell] = value
                    ws[cell].number_format = '#,##0.00'
                else:
                    ws[cell] = value
            elif key in quotation_data.get('quotation_calculation', {}).get('staff_breakdown', {}):
                ws[cell] = quotation_data['quotation_calculation']['staff_breakdown'][key]
            elif key in quotation_data.get('variables', {}):
                ws[cell] = quotation_data['variables'][key]

        # Save to BytesIO object instead of file
        from io import BytesIO
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        
        return excel_file
    except Exception as e:
        print(f"Error populating Excel template: {str(e)}")
        return None

def populate_word_template(quotation_data, proposal_summary_data):
    try:
        # Load the Word template
        template_path = os.path.join(os.path.dirname(__file__), 'static', 'PI QUOTE TEMPLATE Word.docx')

        if not os.path.exists(template_path):
            print(f"Word template not found at: {template_path}")
            return None

        print(f"Loading Word template from: {template_path}")
        doc = Document(template_path)

        # Extract data from quotation_data
        quotation_calc = quotation_data.get('quotation_calculation', {})
        variables = quotation_data.get('variables', {})

        # Get the first file's data (assuming single file for now)
        file_data = next(iter(proposal_summary_data.values()))

        # Get the proposal summary data
        proposal_data = file_data.get('proposal_summary', {})
        headers = proposal_data.get('headers', [])
        values = proposal_data.get('values', [])
        data_map = dict(zip(headers, values))

        # Also build a map for extensions section
        extensions_data = file_data.get('extensions', {})
        extensions_headers = extensions_data.get('headers', [])
        extensions_values = extensions_data.get('values', [])
        data_map_extensions = dict(zip(extensions_headers, extensions_values))

        # Prepare extensions list - get all extensions where value is 'yes'
        selected_extensions = [header for header, value in zip(extensions_headers, extensions_values) if str(value).lower() == 'yes']
        # Format extensions vertically with the required statement
        if selected_extensions:
            extensions_text = "\n".join([f"{ext} - Max 10% of Limit of Indemnity." for ext in selected_extensions])
        else:
            extensions_text = "None"

        # Helper function to format numbers as whole numbers with commas
        def format_as_whole_number(value):
            try:
                # Convert to float first, then to int to remove decimals, then format with commas
                num = int(float(str(value).replace(',', '')))
                return f"{num:,}"
            except (ValueError, TypeError):
                return str(value)

        # Combine all data into a single dictionary for template replacement
        template_data = {
            "Name of Proposer / Insured": data_map.get("Name of Proposer / Insured", ""),
            "Name of Cedant / Reinsured": data_map.get("Name of Cedant / Reinsured", ""),
            "Name of Broker": data_map.get("Name of Broker", ""),
            "Period of Cover": data_map.get("Period of Cover ", "") or data_map.get("Period of Cover", ""),
            "Number of Business Partners": str(int(float(str(data_map.get("Number of Business Partners", 0)).replace(',', '')))),
            "Number of Qualified staff": str(int(float(str(data_map.get("Number of Qualified staff", 0)).replace(',', '')))),
            "Number of Unqualified Staff Employed": str(int(float(str(data_map.get("Number of Unqualified Staff", 0)).replace(',', '')))),
            "Limit of Indemnity (Cover Limit)": format_as_whole_number(data_map.get("Limit of Indemnity (Cover Limit)", 0)),
            "Estimated Annual Income": format_as_whole_number(data_map.get("Estimated Annual Income", 0)),
            " extensions ": extensions_text,  # Note the spaces to match template placeholder
            "total_premium": format_as_whole_number(variables.get("total_premium", 0)),
            "Deductible/Excess Applicable": format_as_whole_number(data_map.get("Deductible/Excess Applicable", 0)),
            "Occupation of the Insured": data_map.get("Occupation of the Insured", "")
        }

        # Function to replace placeholders in paragraphs with formatting
        def fill_placeholders(paragraphs, data):
            for para in paragraphs:
                original_text = para.text
                for key, value in data.items():
                    placeholder = f"{{{{{key}}}}}"
                    if placeholder in para.text:
                        # Clear existing runs and create new formatted content
                        para.clear()

                        # Replace placeholder in the original text
                        new_text = original_text.replace(placeholder, str(value))

                        # Special handling for extensions (vertical layout)
                        if key.strip() == "extensions" and '\n' in str(value):
                            # Split text by colon to get label
                            if ':' in new_text:
                                parts = new_text.split(':', 1)
                                label_part = parts[0].strip()

                                # Add label part (bold)
                                label_run = para.add_run(label_part + ': ')
                                label_run.font.name = 'Times New Roman'
                                label_run.font.size = Pt(14)
                                label_run.font.bold = True

                                # Add first extension on the same line
                                extensions_lines = str(value).split('\n')
                                if extensions_lines:
                                    first_ext_run = para.add_run(extensions_lines[0])
                                    first_ext_run.font.name = 'Times New Roman'
                                    first_ext_run.font.size = Pt(14)
                                    first_ext_run.font.bold = False

                                    # Add remaining extensions on new lines with proper indentation
                                    for ext_line in extensions_lines[1:]:
                                        para.add_run('\n')
                                        # Add spaces to align with the first extension
                                        indent_spaces = ' ' * (len(label_part) + 2)  # +2 for ': '
                                        indent_run = para.add_run(indent_spaces + ext_line)
                                        indent_run.font.name = 'Times New Roman'
                                        indent_run.font.size = Pt(14)
                                        indent_run.font.bold = False
                        else:
                            # Regular handling for other placeholders
                            # Split text by colon to identify labels and values
                            if ':' in new_text:
                                parts = new_text.split(':', 1)  # Split only on first colon
                                label_part = parts[0].strip()
                                value_part = parts[1].strip() if len(parts) > 1 else ""

                                # Add label part (bold)
                                label_run = para.add_run(label_part + ': ')
                                label_run.font.name = 'Times New Roman'
                                label_run.font.size = Pt(14)
                                label_run.font.bold = True

                                # Add value part (normal)
                                if value_part:
                                    value_run = para.add_run(value_part)
                                    value_run.font.name = 'Times New Roman'
                                    value_run.font.size = Pt(14)
                                    value_run.font.bold = False
                            else:
                                # No colon, treat as regular text
                                run = para.add_run(new_text)
                                run.font.name = 'Times New Roman'
                                run.font.size = Pt(14)
                                run.font.bold = False

                        break  # Move to next paragraph after replacement

        # Fill placeholders in the document
        fill_placeholders(doc.paragraphs, template_data)

        # Also fill placeholders in tables (if any exist in the template)
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    fill_placeholders(cell.paragraphs, template_data)

        # Save to BytesIO object instead of file
        from io import BytesIO
        word_file = BytesIO()
        doc.save(word_file)
        word_file.seek(0)

        return word_file
    except Exception as e:
        print(f"Error populating Word template: {str(e)}")
        return None

@app.route('/download_quote', methods=['GET'])
def download_quote():
    try:
        if not proposal_summary:
            print("No proposal summary found")
            return jsonify({"error": "No quotation data available"}), 400

        # Calculate quotation using your existing function
        quotation_data = calculate_quotation(proposal_summary)
        print("Quotation data:", quotation_data)  # Debug log
        
        if "error" in quotation_data:
            print("Error in quotation data:", quotation_data["error"])
            return jsonify({"error": quotation_data["error"]}), 400

        # Get company name from quotation data
        company_name = quotation_data.get('quotation_calculation', {}).get('company_name', '')
        # Get current date
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # Create filename based on whether company name exists
        if company_name and company_name.strip():
            # Clean company name to make it safe for filename
            safe_company_name = "".join(c for c in company_name if c.isalnum() or c in (' ', '-', '_')).strip()
            download_filename = f"{safe_company_name}-{current_date}-quotation.xlsx"
        else:
            # Use date-only format if company name is missing
            download_filename = f"{current_date}-quotation.xlsx"

        # Populate Excel template (in memory)
        excel_file = populate_excel_template(quotation_data)
        if not excel_file:
            print("Failed to generate Excel file")
            return jsonify({"error": "Failed to generate Excel file"}), 500

        try:
            # Create response with the file
            response = make_response(excel_file.getvalue())
            
            # Set headers for Excel file download
            response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            response.headers["Content-Disposition"] = f"attachment; filename*=UTF-8''{quote(download_filename)}"
            
            return response
        except Exception as e:
            print(f"Error sending file: {str(e)}")
            return jsonify({"error": f"Failed to send file: {str(e)}"}), 500

    except Exception as e:
        print(f"Error in download_quote: {str(e)}")
        return jsonify({"error": f"Failed to generate Excel file: {str(e)}"}), 500

@app.route('/download_word_quote', methods=['GET'])
def download_word_quote():
    try:
        if not proposal_summary:
            print("No proposal summary found")
            return jsonify({"error": "No quotation data available"}), 400

        # Calculate quotation using your existing function
        quotation_data = calculate_quotation(proposal_summary)
        print("Quotation data:", quotation_data)  # Debug log

        if "error" in quotation_data:
            print("Error in quotation data:", quotation_data["error"])
            return jsonify({"error": quotation_data["error"]}), 400

        # Get company name from quotation data
        company_name = quotation_data.get('quotation_calculation', {}).get('company_name', '')
        # Get current date
        current_date = datetime.now().strftime('%Y-%m-%d')

        # Create filename based on whether company name exists
        if company_name and company_name.strip():
            # Clean company name to make it safe for filename
            safe_company_name = "".join(c for c in company_name if c.isalnum() or c in (' ', '-', '_')).strip()
            download_filename = f"{safe_company_name}-{current_date}-quotation.docx"
        else:
            # Use date-only format if company name is missing
            download_filename = f"{current_date}-quotation.docx"

        # Populate Word template (in memory)
        word_file = populate_word_template(quotation_data, proposal_summary)
        if not word_file:
            print("Failed to generate Word file")
            return jsonify({"error": "Failed to generate Word file"}), 500

        try:
            # Create response with the file
            response = make_response(word_file.getvalue())

            # Set headers for Word file download
            response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            response.headers["Content-Disposition"] = f"attachment; filename*=UTF-8''{quote(download_filename)}"

            return response
        except Exception as e:
            print(f"Error sending file: {str(e)}")
            return jsonify({"error": f"Failed to send file: {str(e)}"}), 500

    except Exception as e:
        print(f"Error in download_word_quote: {str(e)}")
        return jsonify({"error": f"Failed to generate Word file: {str(e)}"}), 500

def format_number_with_commas(number):
    """
    Formats a number with commas for display purposes.
    Args:
        number: The number to format (can be int, float, or string)
    Returns:
        str: The formatted number with commas
    """
    try:
        # Convert to float first to handle both int and float inputs
        num = float(str(number).replace(',', ''))
        # Format with commas and no decimal places
        return f"{num:,.0f}"
    except (ValueError, TypeError):
        return str(number)

@app.route('/format_number', methods=['POST'])
def format_number():
    """
    API endpoint to format numbers with commas for frontend display.
    Expects a JSON payload with a 'number' field.
    """
    try:
        data = request.get_json()
        if not data or 'number' not in data:
            return jsonify({"error": "No number provided"}), 400
            
        formatted_number = format_number_with_commas(data['number'])
        return jsonify({"formatted_number": formatted_number})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def detect_pdf_type(file_path, min_text_threshold=10, sample_pages=3):
    """
    Check if a PDF is text-based or scanned (image-based).

    Args:
        file_path (str): Path to the PDF file.
        min_text_threshold (int): Minimum number of characters to consider a page as "text-based".
        sample_pages (int): Number of pages to sample (default: 3).

    Returns:
        str: "text-based" or "scanned"
    """
    try:
        doc = fitz.open(file_path)
        num_pages = len(doc)
        pages_to_check = min(sample_pages, num_pages)
        text_pages = 0

        # Sample pages to determine if text-based
        for i in range(pages_to_check):
            text = doc[i].get_text().strip()
            if len(text) >= min_text_threshold:
                text_pages += 1

        doc.close()

        # Determine PDF type based on sampled pages
        pdf_type = "text-based" if text_pages >= pages_to_check // 2 + 1 else "scanned"
        
        # Print detailed information
        print(f"\n=== PDF Analysis ===")
        print(f"File: {os.path.basename(file_path)}")
        print(f"Type: {'✅ Text-based' if pdf_type == 'text-based' else '🖼️ Scanned'}")
        print(f"Total Pages: {num_pages}")
        print(f"Pages Sampled: {pages_to_check}")
        print(f"Text Pages Found: {text_pages}")
        print("===================\n")

        return pdf_type

    except Exception as e:
        print(f"Error detecting PDF type: {str(e)}")
        return "unknown"

@app.route('/edit_proposal_field', methods=['POST'])
def edit_proposal_field():
    """
    Edits a specific field in the proposal_summary for a given filename and section.
    Expects JSON: { filename, section, field, value }
    """
    global proposal_summary
    data = request.get_json()
    filename = data.get('filename')
    section = data.get('section')
    field = data.get('field')
    value = data.get('value')

    if not proposal_summary:
        return jsonify({"error": "No proposal data available. Please upload a proposal first."}), 400
    if filename not in proposal_summary:
        return jsonify({"error": f"Filename '{filename}' not found in proposal data."}), 400
    if section not in proposal_summary[filename]:
        return jsonify({"error": f"Section '{section}' not found in proposal data for file '{filename}'."}), 400

    section_data = proposal_summary[filename][section]
    headers = section_data.get('headers', [])
    values = section_data.get('values', [])

    if field not in headers:
        return jsonify({"error": f"Field '{field}' not found in section '{section}' for file '{filename}'."}), 400

    idx = headers.index(field)

    # Ensure values list is long enough
    while len(values) <= idx:
        values.append(None)

    values[idx] = value
    section_data['values'] = values
    proposal_summary[filename][section] = section_data
    return jsonify({"message": f"Field '{field}' in section '{section}' for file '{filename}' updated successfully."}), 200

if __name__ == '__main__':
    # Initialize CSV logging
    initialize_csv_log()
    app.run(host="127.0.0.1", debug=True, port=8080)
